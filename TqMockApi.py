"""
TqMockApi - 模拟天勤交易API的实现

该模块提供了一个模拟天勤交易API的实现，用于在不连接真实交易系统的情况下
测试交易策略。它通过预加载的历史数据模拟实时行情推送，并提供持仓和账户管理功能。
"""

import pandas as pd
import time
import os
from datetime import datetime
from typing import Dict, Optional, Any

from tqsdk import BacktestFinished

from constant.constant import TIME_COL, PRICE_COL


class TqMockApi:
    """
    天勤交易API的模拟实现类
    
    该类模拟了天勤交易API的主要功能，包括K线数据获取、持仓查询、账户信息查询等，
    通过预加载的历史数据模拟实时行情推送，适用于策略回测和单元测试。
    
    Attributes:
        data_dir (str): 测试数据存储目录
        _klines (Dict): 存储所有获取的K线数据
        _positions (Dict): 存储持仓信息
        _account (Dict): 模拟账户信息
        _current_index (int): 当前数据索引
        _data (pd.DataFrame): 加载的完整数据
        _start_dt (datetime, optional): 回测开始时间
        _end_dt (datetime, optional): 回测结束时间
    """

    def __init__(self, data_path: Optional[str] = None, initial_balance: float = 1000000,
                 start_dt: Optional[datetime] = None, end_dt: Optional[datetime] = None):
        """
        初始化模拟API

        Args:
            data_path (str, optional): 测试数据文件路径，如果为None则使用默认路径
            initial_balance (float): 初始账户余额
            start_dt (datetime, optional): 回测开始时间，如果为None则使用数据中的第一个时间点
            end_dt (datetime, optional): 回测结束时间，如果为None则使用数据中的最后一个时间点
        """
        # 保存回测时间范围
        self._start_dt = start_dt
        self._end_dt = end_dt
        # 初始化内部状态
        self._klines: Dict[str, Dict[str, Any]] = {}  # 存储所有获取的K线数据
        self._positions: Dict[str, Dict[str, int]] = {}  # 存储持仓信息
        self._account: Dict[str, float] = {"balance": initial_balance}  # 模拟账户信息
        self._data: Optional[pd.DataFrame] = None  # 加载的完整数据

        # 性能优化：datetime转换缓存
        self._datetime_cache: Dict[float, str] = {}  # 缓存timestamp到可读字符串的转换

        self._load_test_data(data_path)

        # 初始化当前索引为start_dt对应的索引，如果没有指定start_dt则使用第一个索引
        self._current_index: int = 0
        if self._data is not None and len(self._data) > 0:
            if self._start_dt is not None:
                # 找到最接近start_dt的索引
                # 注意：self._data['datetime']中的时间戳已经应用了-28800秒的时区调整
                # 所以这里计算start_dt_ns时也需要应用相同的调整，确保比较的是同一时区的时间
                start_dt_ns = ((self._start_dt - datetime(1970, 1, 1)).total_seconds() - 28800) * 1e9
                closest_idx = (self._data['datetime'] - start_dt_ns).abs().idxmin()
                self._current_index = closest_idx

    def _load_test_data(self, path: str) -> None:
        """
        从本地文件加载测试数据
        
        Args:
            path (str): 测试数据文件路径
        
        Raises:
            FileNotFoundError: 如果指定的文件不存在
            ValueError: 如果文件格式不正确
        """
        if not os.path.exists(path):
            raise FileNotFoundError(f"测试数据文件不存在: {path}")

        try:
            # 读取CSV文件
            df = pd.read_csv(path)

            # 提取时间和价格数据
            time_list = df[TIME_COL].to_list()
            price_list = df[PRICE_COL].to_list()

            # 将字符串转换为datetime对象
            datetime_list = [
                datetime.strptime(t, "%Y-%m-%d %H:%M:%S")
                for t in time_list
            ]

            # 创建完整的DataFrame，模拟K线数据格式
            full_data = pd.DataFrame({
                # 转换为纳秒时间戳，与天勤API保持一致
                "datetime": [
                    # 减去8小时的秒数以调整时区
                    ((t - datetime(1970, 1, 1)).total_seconds() - 28800) * 1e9
                    for t in datetime_list
                ],
                "datetime_obj": datetime_list,  # 保存原始datetime对象用于过滤
                "open": price_list,
                # ToDo(hm): 可以去掉这些模拟
                "high": [p + 1 for p in price_list],  # 简化模拟，高价比开盘价高1
                "low": [p - 1 for p in price_list],  # 简化模拟，低价比开盘价低1
                "close": price_list,  # 简化模拟，收盘价等于开盘价
                "volume": [100] * len(price_list)  # 简化模拟，成交量固定为100
            })
            self._data = full_data

        except KeyError as e:
            raise ValueError(f"数据文件格式不正确，缺少必要的列: {e}")
        except Exception as e:
            raise ValueError(f"加载测试数据失败: {e}")

    def get_kline_serial(self, contract_code: str, period: str, data_length: int) -> pd.DataFrame:
        """
        获取K线数据，返回的是数据引用
        
        Args:
            contract_code (str): 合约代码
            period (str): K线周期，如 "1m", "5m", "1h", "1d"
            data_length (int): 需要获取的K线数量，向前获取的数据量
            
        Returns:
            pd.DataFrame: K线数据，包含datetime, open, high, low, close, volume等列
        """
        # 计算数据起始索引（向前取data_length个数据点）
        start_idx = max(0, self._current_index - data_length + 1)

        # 创建初始K线数据（从start_idx到当前索引）
        klines = self._data.iloc[start_idx:self._current_index + 1].copy()

        # 存储引用以便后续更新
        self._klines[contract_code] = {
            "data": klines,
            "period": period,
            "data_length": data_length
        }

        return self._klines[contract_code]["data"]

    def get_position(self, contract_code: str) -> Dict[str, int]:
        """
        获取持仓信息
        
        Args:
            contract_code (str): 合约代码
            
        Returns:
            Dict[str, int]: 持仓信息，包含多空方向的持仓量
        """
        # 如果合约不存在，初始化空持仓
        if contract_code not in self._positions:
            self._positions[contract_code] = {"volume_long": 0, "volume_short": 0}

        return self._positions[contract_code]

    def get_account(self) -> Dict[str, float]:
        """
        获取账户信息
        
        Returns:
            Dict[str, float]: 账户信息，包含账户余额等
        """
        return self._account

    def wait_update(self, deadline: Optional[float] = None) -> bool:
        """
        等待数据更新，模拟真实API的行为

        Args:
            deadline (float, optional): 最长等待时间，单位为秒

        Returns:
            bool: 如果有更新返回True

        Raises:
            BacktestFinished: 如果回测结束（没有更多数据或超过结束时间）
        """
        # 检查是否还有更多数据
        if self._current_index >= len(self._data) - 1:
            print("Backtest finished, no more data")
            raise BacktestFinished("回测结束")

        # 更新索引
        self._current_index += 1

        # 检查最新数据是否超过了结束时间
        if self._end_dt is not None:
            current_datetime_obj = self._data.iloc[self._current_index]['datetime_obj']
            if current_datetime_obj > self._end_dt:
                print(f"Backtest finished, current time {current_datetime_obj} exceeds end time {self._end_dt}")
                raise BacktestFinished("回测结束")

        # 更新所有已获取的K线数据
        self._update_all_klines()
        return True

    def _update_all_klines(self) -> None:
        """
        更新所有已获取的K线数据 - 优化版本
        """
        for contract, kline_info in self._klines.items():
            data_length = kline_info["data_length"]

            # 计算新的数据范围（向前取data_length个数据点）
            start_idx = max(0, self._current_index - data_length + 1)
            end_idx = self._current_index + 1

            # 获取当前DataFrame引用
            current_df = kline_info["data"]

            # 检查是否需要更新（避免不必要的操作）
            if len(current_df) > 0 and end_idx - start_idx == len(current_df):
                # 大小匹配，只需要更新数据，不需要重建DataFrame
                self._update_existing_dataframe(current_df, start_idx, end_idx)
            else:
                # 大小不匹配，需要重建DataFrame
                self._rebuild_dataframe(current_df, start_idx, end_idx)

    def _update_existing_dataframe(self, current_df: pd.DataFrame, start_idx: int, end_idx: int) -> None:
        """
        更新现有DataFrame的数据（大小匹配的情况）
        """
        # 直接从源数据更新，避免创建中间副本
        source_data = self._data.iloc[start_idx:end_idx]

        # 批量更新基础列（避免逐列循环）
        base_columns = ['datetime', 'open', 'high', 'low', 'close', 'volume']
        for col in base_columns:
            if col in source_data.columns and col in current_df.columns:
                current_df[col] = source_data[col].values

        # 处理特殊列
        if 'datetime_readable' in current_df.columns:
            # 使用缓存的datetime转换或批量转换
            current_df['datetime_readable'] = self._get_datetime_readable_batch(source_data['datetime'])

    def _rebuild_dataframe(self, current_df: pd.DataFrame, start_idx: int, end_idx: int) -> None:
        """
        重建DataFrame（大小不匹配的情况）
        """
        # 获取新数据（避免不必要的copy）
        new_data = self._data.iloc[start_idx:end_idx]

        # 保存需要保留的额外列信息
        extra_columns = [col for col in current_df.columns if col not in new_data.columns]
        extra_data = {}

        # 处理额外列
        if extra_columns and len(current_df) > 0:
            for col in extra_columns:
                if col == 'datetime_readable':
                    extra_data[col] = self._get_datetime_readable_batch(new_data['datetime'])
                else:
                    # 使用第一个值填充
                    extra_data[col] = [current_df[col].iloc[0]] * len(new_data)

        # 高效重建DataFrame
        current_df.drop(current_df.index, inplace=True)

        # 批量添加所有列
        all_data = {}
        for col in new_data.columns:
            all_data[col] = new_data[col].values
        for col, data in extra_data.items():
            all_data[col] = data

        # 一次性添加所有数据
        for col, data in all_data.items():
            current_df[col] = data

    def _get_datetime_readable_batch(self, datetime_series: pd.Series) -> list:
        """
        批量转换datetime为可读字符串，使用缓存和向量化操作提升性能
        """
        result = []
        uncached_timestamps = []
        uncached_indices = []

        # 检查缓存，收集未缓存的时间戳
        for i, ts in enumerate(datetime_series):
            if ts in self._datetime_cache:
                result.append(self._datetime_cache[ts])
            else:
                result.append(None)  # 占位符
                uncached_timestamps.append(ts)
                uncached_indices.append(i)

        # 批量处理未缓存的时间戳
        if uncached_timestamps:
            adjusted_timestamps = pd.Series(uncached_timestamps) + 28800 * 1e9
            datetime_objects = pd.to_datetime(adjusted_timestamps, unit='ns')
            formatted_strings = datetime_objects.dt.strftime('%Y-%m-%d %H:%M:%S').tolist()

            # 更新缓存和结果
            for idx, ts, formatted_str in zip(uncached_indices, uncached_timestamps, formatted_strings):
                self._datetime_cache[ts] = formatted_str
                result[idx] = formatted_str

        return result

    def is_changing(self, klines: pd.DataFrame) -> bool:
        """
        检查K线数据是否有更新
        
        Args:
            klines (pd.DataFrame): 要检查的K线数据
            
        Returns:
            bool: 如果数据有更新返回True，否则返回False
        """
        # 在这个简化实现中，每次wait_update都认为数据有更新
        return True

    def close(self):
        """
        关闭API连接
        
        在真实的TqApi中，这个方法会关闭与服务器的连接。
        在这个模拟实现中，它只是一个空方法，用于兼容性。
        """
        print("Closing TqMockApi (no actual connection to close)")
        pass

    def insert_order(self, contract_code: str, direction: str,
                     offset: str, volume: int, limit_price: float) -> Dict[str, Any]:
        """
        下单接口（模拟实现，可扩展）
        
        Args:
            contract_code (str): 合约代码
            direction (str): 方向，"BUY"或"SELL"
            offset (str): 开平，"OPEN"或"CLOSE"
            volume (int): 数量
            limit_price (float): 限价
            
        Returns:
            Dict[str, Any]: 订单信息
        """
        # 这里可以实现模拟下单逻辑
        order_id = f"order_{int(time.time() * 1000)}"
        return {"order_id": order_id, "status": "FINISHED"}


# 示例用法
if __name__ == '__main__':
    # 生成模拟数据
    # print("生成模拟数据...")
    # time_list, price_list = generate_mock_data(n=117, volatility=0.005, trend=0.0005)

    # 保存数据到本地文件
    # print("保存数据到本地文件...")
    # save_test_data(time_list, price_list)

    # 初始化API
    print("初始化模拟API...")
    api = TqMockApi(data_path="/unit_test/test_data/expected/test_sr505_0310_price.csv")

    # 获取K线数据
    print("获取K线数据...")
    klines = api.get_kline_serial("sr505", "1m", 100)
    print(f"初始K线数据形状: {klines.shape}")

    # 模拟数据更新
    print("\n模拟数据更新:")
    for i in range(10):
        if api.wait_update():
            print(f"更新 #{i + 1}: {klines.iloc[-1]['datetime']} - 开:{klines.iloc[-1]['open']:.2f}, "
                  f"高:{klines.iloc[-1]['high']:.2f}, 低:{klines.iloc[-1]['low']:.2f}, "
                  f"收:{klines.iloc[-1]['close']:.2f}")
        else:
            print("没有更多数据")
            break
